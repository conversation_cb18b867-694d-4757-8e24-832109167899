const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  user: { type: String, ref: 'User', unique: true },
  priorTopPicks: { type: [{ type: String, ref: 'User' }], default: [] },
  priorTopPicksSameDatingGoals: { type: [{ type: String, ref: 'User' }], default: [] },
  priorTopPicksSimilarInterests: { type: [{ type: String, ref: 'User' }], default: [] },
});

schema.index({
  user: 1,
});

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('TopPicksExclusionList', schema);
